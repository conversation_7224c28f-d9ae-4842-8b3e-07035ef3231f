import { <PERSON>, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { UserCheck, Calendar, Shield, Building2, Briefcase, MapPin } from "lucide-react";
import AvatarDisplay from "@/features/profile/components/AvatarDisplay";
import { useNavigate } from "react-router-dom";
import { getIndustryTheme, IndustryIcon } from '@/utils/industryIcons';
import FollowButton from '@/features/social/components/FollowButton';

// Define the member type for the component
export type MemberItem = {
  id: string;
  first_name: string | null;
  last_name: string | null;
  joinedAt: string;
  job_title: string | null;
  organisation_name: string | null;
  bio: string | null;
  headline_bio: string | null;
  avatar_url: string | null;
  sustainability_professional: boolean;
  linkedin_url: string | null;
  twitter_url: string | null;
  instagram_url: string | null;
  tiktok_url: string | null;
  location_id: string | null;
  location_name?: string | null;
  primaryNetZeroCategory?: string | null;
  contact_email?: string | null;
  contact_phone_landline?: string | null;
  contact_phone_mobile?: string | null;
  // Extended fields for filtering
  main_industry_id?: string | null;
  netZeroInterests?: Array<{ id: string; name: string; category?: { name: string } }>;
  industry?: { id: string; name: string; parent?: { id: string; name: string } } | null;
};

interface MembersListProps {
  members: MemberItem[];
}

const MembersList = ({ members }: MembersListProps) => {
  const navigate = useNavigate();

  const handleMemberClick = (memberId: string) => {
    navigate(`/members/${memberId}`);
  };

  return (
    <div className="grid gap-4 sm:gap-6 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3">
      {members.map((member) => (
        <Card 
          key={member.id} 
          className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-[1.02] flex flex-col h-full"
          onClick={() => handleMemberClick(member.id)}
        >
          <CardHeader className="pb-3 relative">
            {/* Follow Button - positioned absolutely in top-right */}
            <div
              className="absolute top-3 right-3 z-10"
              onClick={(e) => {
                // Prevent card click when clicking follow button
                e.stopPropagation();
              }}
            >
              <FollowButton
                userId={member.id}
                variant="outline"
                size="sm"
                showIcon={false}
                className="min-w-[70px] text-xs"
                onFollowChange={(isFollowing) => {
                  // Optional: Handle follow state change
                  console.log(`${isFollowing ? 'Following' : 'Unfollowed'} ${member.first_name} ${member.last_name}`);
                }}
              />
            </div>

            <div className="flex items-start gap-3">
              <AvatarDisplay
                avatarUrl={member.avatar_url}
                firstName={member.first_name}
                lastName={member.last_name}
                email={`${member.first_name?.toLowerCase() || ''}${member.last_name?.toLowerCase() || ''}@example.com`}
                size="lg"
                shape="square"
              />
              <div className="flex-1 min-w-0 pr-16 sm:pr-20">
                {/* Name - Allow natural wrapping */}
                <div className="mb-2">
                  <CardTitle className="text-lg leading-tight break-words">
                    {`${member.first_name || ''} ${member.last_name || ''}`.trim() || 'Anonymous User'}
                  </CardTitle>
                </div>
                
                {/* Job Title - Allow natural wrapping */}
                <div className="flex items-start gap-2 text-sm font-medium text-zinc-700 mb-1">
                  <Briefcase className="w-4 h-4 text-primary flex-shrink-0 mt-0.5" />
                  <span className="leading-relaxed break-words">
                    {member.job_title || <span className="italic text-muted-foreground">No job title specified</span>}
                  </span>
                </div>
                
                {/* Organisation - Allow natural wrapping */}
                <div className="flex items-start gap-2 text-sm font-medium text-zinc-700 mb-1">
                  <Building2 className="w-4 h-4 text-primary flex-shrink-0 mt-0.5" />
                  <span className="leading-relaxed break-words">
                    {member.organisation_name || <span className="italic text-muted-foreground">No organisation specified</span>}
                  </span>
                </div>
                
                {/* Location - Allow natural wrapping */}
                <div className="flex items-start gap-2 text-sm font-medium text-emerald-700 mb-1">
                  <MapPin className="w-4 h-4 text-emerald-700 flex-shrink-0 mt-0.5" />
                  <span className="leading-relaxed break-words">
                    {member.location_name || <span className="italic text-muted-foreground">No location specified</span>}
                  </span>
                </div>
                
                {/* Industry - Allow natural wrapping */}
                {member.industry && (
                  <div className="flex items-start gap-2 text-sm font-medium mb-1">
                    {member.industry.parent ? (
                      <>
                        <IndustryIcon 
                          industryName={member.industry.parent.name} 
                          size="sm" 
                          className="w-4 h-4 flex-shrink-0 mt-0.5" 
                        />
                        <span className={`leading-relaxed ${getIndustryTheme(member.industry.parent.name).textColor}`}>
                          {member.industry.name}
                        </span>
                      </>
                    ) : (
                      <>
                        <Building2 className="w-4 h-4 text-primary flex-shrink-0 mt-0.5" />
                        <span className="leading-relaxed text-zinc-700">
                          {member.industry.name}
                        </span>
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>
            
            {/* Sustainability Professional Badge - Always maintain consistent spacing */}
            <div className="mt-3 min-h-[32px] flex items-start">
              {member.sustainability_professional && (
                <Badge className="bg-emerald-600 hover:bg-emerald-700 text-white">
                  <Shield className="w-3 h-3 mr-1" />
                  Sustainability Professional
                </Badge>
              )}
            </div>
            
            {/* Primary Net Zero Area */}
            <div className="flex items-start gap-2 mt-3">
              <span className="text-sm font-medium text-zinc-700 mr-2">Primary Area of Interest:</span>
              {member.primaryNetZeroCategory ? (
                <Badge 
                  variant="default" 
                  className="bg-green-600 hover:bg-green-700 text-white border-green-700 flex items-center gap-1"
                >
                  <Building2 className="w-3 h-3 mr-1" />
                  {member.primaryNetZeroCategory}
                </Badge>
              ) : (
                <span className="italic text-muted-foreground text-sm">No area of interest specified</span>
              )}
            </div>
          </CardHeader>
          <CardContent className="pt-4 flex flex-col flex-grow">
            <div className="flex flex-col gap-3 flex-grow">
              {/* Headline Bio */}
              <div className="mb-3">
                <span className="text-sm font-medium text-zinc-700">Headline Bio:</span>
                <div className="text-sm font-medium text-emerald-700 leading-relaxed mt-1">
                  {member.headline_bio ? (
                    <p>{member.headline_bio}</p>
                  ) : (
                    <span className="italic text-muted-foreground">No headline bio provided</span>
                  )}
                </div>
              </div>
              
              {/* Spacer to push join date to bottom */}
              <div className="flex-grow"></div>
              
              <div className="flex items-center gap-1 text-muted-foreground text-sm mt-auto">
                <Calendar className="w-3 h-3" />
                Joined {new Date(member.joinedAt).toLocaleDateString()}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default MembersList;
