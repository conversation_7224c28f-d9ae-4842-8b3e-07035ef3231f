import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Building2, Factory, Tractor, Plug, Users, DollarSign, Home, BarChart3, Settings, Briefcase, Wrench, FlaskConical, ShoppingCart, HeartHandshake, GraduationCap, Hospital, ShieldCheck, Globe, Utensils, Plane, Ship, Hammer, Construction, BookOpen, Handshake, PiggyBank, Rocket, CloudSun, FlaskRound, ShoppingBag, Dumbbell, Palette, Music, Film, Monitor, Smartphone, Wifi, Globe2, Map, Anchor, Beer, Coffee, Wine, Scissors, ShoppingBasket, ShoppingCart as Cart, Package, Truck as DeliveryTruck, Car, Bus, Train, Airplay, Wind, Sun, Droplet, Flame, Battery, Lightbulb, Thermometer, Snowflake, Cloud, CloudRain, CloudLightning, CloudDrizzle, CloudSnow, CloudFog, CloudHail, CloudMoon, Cloudy, X } from 'lucide-react';
import { UKIndustryService } from '@/services/ukIndustryService';
import { IndustryIcon } from '../../utils/industryIcons';
import type { 
  UKIndustryWithChildren, 
  IndustrySelectionState 
} from '@/types/uk-industries.types';
import { Label } from '@/components/ui/label';

interface IndustrySelectorProps {
  selectedIndustryId?: string | null;
  selectedTargetIndustryIds?: string[];
  onIndustryChange?: (industryId: string | null) => void;
  onTargetIndustriesChange?: (industryIds: string[]) => void;
  primaryTargetIndustryId?: string | null;
  onPrimaryTargetIndustryChange?: (industryId: string | undefined) => void;
  mode: 'single' | 'multi' | 'both';
  title?: string;
  description?: string;
  singleSelectLabel?: string;
  multiSelectLabel?: string;
  maxSelections?: number;
  allowParentSelection?: boolean;
  className?: string;
}

// Icon mapping for main industries (example, expand as needed)
const industryIcons: Record<string, JSX.Element> = {
  'Manufacturing & Production': <Factory className="w-5 h-5 text-primary" />,
  'Services': <Users className="w-5 h-5 text-primary" />,
  'Energy & Utilities': <Plug className="w-5 h-5 text-primary" />,
  'Primary Industries': <Tractor className="w-5 h-5 text-primary" />,
  'Construction & Real Estate': <Hammer className="w-5 h-5 text-primary" />,
  'Healthcare & Education': <Hospital className="w-5 h-5 text-primary" />,
  'Social Care': <HeartHandshake className="w-5 h-5 text-primary" />,
  // Add more as needed
};

// Accordion row for parent industry, matching NetZeroCategorySelector style
const ParentIndustryAccordion: React.FC<{
  parent: UKIndustryWithChildren;
  expanded: boolean;
  onToggle: () => void;
  childrenContent: React.ReactNode;
}> = ({ parent, expanded, onToggle, childrenContent }) => (
  <Collapsible open={expanded} onOpenChange={onToggle}>
    <CollapsibleTrigger asChild>
      <Button
        variant="ghost"
        className="w-full justify-between p-4 h-auto border rounded-lg hover:bg-gray-50 flex items-center"
        type="button"
      >
        <div className="flex items-center gap-3 flex-1 min-w-0">
          {expanded ? (
            <ChevronDown className="w-4 h-4" />
          ) : (
            <ChevronRight className="w-4 h-4" />
          )}
          <IndustryIcon industryName={parent.name} size="xl" />
          <div className="flex-1 min-w-0">
            <span className="font-bold break-words whitespace-normal text-left flex items-center gap-2">
              {parent.name}
            </span>
            {parent.description && (
              <p className="text-xs text-muted-foreground mt-1 break-words whitespace-normal text-left">
                {parent.description}
              </p>
            )}
          </div>
        </div>
      </Button>
    </CollapsibleTrigger>
    <CollapsibleContent className="mt-2 ml-4 space-y-2">
      {childrenContent}
    </CollapsibleContent>
  </Collapsible>
);

export const IndustrySelector: React.FC<IndustrySelectorProps> = ({
  selectedIndustryId,
  selectedTargetIndustryIds = [],
  onIndustryChange,
  onTargetIndustriesChange,
  primaryTargetIndustryId,
  onPrimaryTargetIndustryChange,
  mode,
  title = "Industry Selection",
  description = "Select the relevant industries",
  singleSelectLabel = "Your Industry",
  multiSelectLabel = "Target Industries",
  maxSelections,
  allowParentSelection = false,
  className = ""
}) => {
  const [industries, setIndustries] = useState<UKIndustryWithChildren[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectionState, setSelectionState] = useState<IndustrySelectionState>({
    selectedIndustry: selectedIndustryId || null,
    selectedTargetIndustries: new Set(selectedTargetIndustryIds),
    expandedCategories: new Set()
  });

  useEffect(() => {
    loadIndustries();
  }, []);

  useEffect(() => {
    setSelectionState(prev => {
      const selectedIndustryChanged = prev.selectedIndustry !== (selectedIndustryId || null);
      const selectedTargetIndustriesChanged = !(
        prev.selectedTargetIndustries.size === (selectedTargetIndustryIds?.length || 0) &&
        Array.from(prev.selectedTargetIndustries).every(id => selectedTargetIndustryIds?.includes(id))
      );
      if (selectedIndustryChanged || selectedTargetIndustriesChanged) {
        return {
          ...prev,
          selectedIndustry: selectedIndustryId || null,
          selectedTargetIndustries: new Set(selectedTargetIndustryIds)
        };
      }
      return prev;
    });
  }, [selectedIndustryId, selectedTargetIndustryIds]);

  const loadIndustries = async () => {
    try {
      setLoading(true);
      const data = await UKIndustryService.getAllIndustriesWithChildren();
      setIndustries(data);
      
      // Auto-expand categories that have selected items
      const expandedCategories = new Set<string>();
      data.forEach(parent => {
        const hasSelectedChild = parent.children.some(child => 
          selectedIndustryId === child.id || selectedTargetIndustryIds.includes(child.id)
        );
        const hasSelectedParent = selectedIndustryId === parent.id || selectedTargetIndustryIds.includes(parent.id);
        
        if (hasSelectedChild || hasSelectedParent) {
          expandedCategories.add(parent.id);
        }
      });
      
      setSelectionState(prev => ({
        ...prev,
        expandedCategories
      }));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load industries');
    } finally {
      setLoading(false);
    }
  };

  const handleSingleSelect = (industryId: string) => {
    const newSelection = selectionState.selectedIndustry === industryId ? null : industryId;
    setSelectionState(prev => ({
      ...prev,
      selectedIndustry: newSelection
    }));
    onIndustryChange?.(newSelection);
  };

  const handleMultiSelect = (industryId: string) => {
    const newSelected = new Set(selectionState.selectedTargetIndustries);
    
    if (newSelected.has(industryId)) {
      newSelected.delete(industryId);
    } else {
      // Check max selections limit
      if (maxSelections && newSelected.size >= maxSelections) {
        return; // Don't allow more selections
      }
      newSelected.add(industryId);
    }
    
    const newSelectedArray = Array.from(newSelected);
    setSelectionState(prev => ({
      ...prev,
      selectedTargetIndustries: newSelected
    }));
    onTargetIndustriesChange?.(newSelectedArray);
  };

  const toggleCategoryExpansion = (categoryId: string) => {
    setSelectionState(prev => {
      const newExpanded = new Set(prev.expandedCategories);
      if (newExpanded.has(categoryId)) {
        newExpanded.delete(categoryId);
      } else {
        newExpanded.add(categoryId);
      }
      return {
        ...prev,
        expandedCategories: newExpanded
      };
    });
  };

  const getSelectedCount = () => selectionState.selectedTargetIndustries.size;

  const clearAllSelections = () => {
    setSelectionState(prev => ({
      ...prev,
      selectedIndustry: null,
      selectedTargetIndustries: new Set()
    }));
    onIndustryChange?.(null);
    onTargetIndustriesChange?.([]);
    onPrimaryTargetIndustryChange?.(undefined);
  };

  const getIndustryDisplayName = (industryId: string) => {
    for (const parent of industries) {
      if (parent.id === industryId) {
        return parent.name;
      }
      const child = parent.children.find(c => c.id === industryId);
      if (child) {
        return child.name;
      }
    }
    return industryId;
  };

  const getTotalIndustries = () => {
    return industries.reduce((total, parent) => total + parent.children.length, 0);
  };

  const renderIndustryOption = (industry: UKIndustryWithChildren, isChild = false) => {
    const canSelect = allowParentSelection || isChild;
    const isSelectedSingle = selectionState.selectedIndustry === industry.id;
    const isSelectedMulti = selectionState.selectedTargetIndustries.has(industry.id);
    const isMaxReached = maxSelections && getSelectedCount() >= maxSelections && !isSelectedMulti;
    const isPrimary = primaryTargetIndustryId === industry.id;

    return (
      <div key={industry.id} className={`flex items-center gap-3 p-3 rounded-lg border bg-card hover:bg-muted/30 ${isChild ? 'ml-6' : ''}`}>
        {mode === 'single' && canSelect && isChild && (
          <RadioGroupItem
            value={industry.id}
            id={`single-${industry.id}`}
            checked={isSelectedSingle}
            disabled={!canSelect}
          />
        )}
        {(mode === 'multi' || mode === 'both') && canSelect && (
          <Checkbox
            id={`multi-${industry.id}`}
            checked={isSelectedMulti}
            onCheckedChange={() => handleMultiSelect(industry.id)}
            disabled={!canSelect || isMaxReached}
          />
        )}
        <div className="flex-1 min-w-0">
          <label
            htmlFor={mode === 'single' ? `single-${industry.id}` : `multi-${industry.id}`}
            className={`text-sm font-medium cursor-pointer ${!canSelect ? 'text-muted-foreground' : ''} break-words whitespace-normal text-left`}
          >
            {industry.name}
          </label>
          {industry.description && (
            <p className="text-xs text-muted-foreground mt-1 break-words whitespace-normal text-left">
              {industry.description}
            </p>
          )}
        </div>
        {(mode === 'multi' || mode === 'both') && isChild && isSelectedMulti && onPrimaryTargetIndustryChange && (
          <Button
            size="sm"
            variant={isPrimary ? "default" : "outline"}
            onClick={() => onPrimaryTargetIndustryChange(isPrimary ? undefined : industry.id)}
            className="text-xs ml-2"
          >
            {isPrimary ? "Primary" : "Set Primary"}
          </Button>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5 text-blue-600" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">Loading industries...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5 text-blue-600" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600">Error: {error}</p>
            <Button onClick={loadIndustries} variant="outline" className="mt-4">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="w-5 h-5 text-blue-600" />
          {title}
        </CardTitle>
        <CardDescription>
          {description}
          {maxSelections && mode !== 'single' && (
            <span className="block mt-1 text-sm">
              Selected: {getSelectedCount()}/{maxSelections}
            </span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Selected Items Summary */}
        {((mode === 'single' && selectionState.selectedIndustry) || 
          (mode !== 'single' && getSelectedCount() > 0)) && (
          <div className="sticky top-20 z-10 mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-md">
            <h3 className="font-semibold text-blue-900 mb-2">
              {mode === 'single' 
                ? `Selected Industry (1):` 
                : `Selected Industries (${getSelectedCount()}${selectionState.selectedIndustry ? ' + 1 main' : ''}):`}
            </h3>
            <div className="flex flex-wrap gap-2 max-h-40 overflow-y-auto">
              {/* Single selection display */}
              {mode === 'single' && selectionState.selectedIndustry && (
                <span 
                  className="inline-flex items-center px-3 py-1 bg-blue-600 text-white text-xs font-medium rounded-full"
                >
                  {getIndustryDisplayName(selectionState.selectedIndustry)}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSingleSelect(selectionState.selectedIndustry!);
                    }}
                    className="ml-2 text-white hover:bg-blue-700 hover:rounded-full p-0.5"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              )}
              
              {/* Multi selection display */}
              {mode !== 'single' && Array.from(selectionState.selectedTargetIndustries).map((industryId) => {
                const displayName = getIndustryDisplayName(industryId);
                const isPrimary = primaryTargetIndustryId === industryId;
                return (
                  <span 
                    key={industryId}
                    className={`inline-flex items-center px-3 py-1 text-xs font-medium rounded-full ${
                      isPrimary 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-blue-100 text-blue-800'
                    }`}
                  >
                    {displayName}
                    {isPrimary && <span className="ml-1 text-xs">(Primary)</span>}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMultiSelect(industryId);
                      }}
                      className={`ml-2 hover:rounded-full p-0.5 ${
                        isPrimary 
                          ? 'text-white hover:bg-blue-700' 
                          : 'text-blue-600 hover:text-blue-800 hover:bg-blue-200'
                      }`}
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                );
              })}

              {/* Main industry display for 'both' mode */}
              {mode === 'both' && selectionState.selectedIndustry && (
                <span 
                  className="inline-flex items-center px-3 py-1 bg-blue-600 text-white text-xs font-medium rounded-full"
                >
                  {getIndustryDisplayName(selectionState.selectedIndustry)} (Main)
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSingleSelect(selectionState.selectedIndustry!);
                    }}
                    className="ml-2 text-white hover:bg-blue-700 hover:rounded-full p-0.5"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              )}
            </div>
            <div className="mt-3 flex space-x-4">
              <button
                onClick={clearAllSelections}
                className="text-sm text-blue-600 hover:text-blue-800 underline"
              >
                Clear all selections
              </button>
              <span className="text-sm text-gray-500">
                {mode === 'single' 
                  ? `Selected: 1 / ${getTotalIndustries()} total`
                  : `Selected: ${getSelectedCount()}${selectionState.selectedIndustry ? ' + 1 main' : ''} / ${getTotalIndustries()} total`}
              </span>
            </div>
          </div>
        )}

        {mode === 'single' && (
          <div>
            <Label className="text-sm font-medium">{singleSelectLabel}</Label>
            <div className="mt-2 space-y-4">
              <RadioGroup
                value={selectionState.selectedIndustry || ''}
                onValueChange={handleSingleSelect}
              >
                {industries.map((parent) => (
                  <ParentIndustryAccordion
                    key={parent.id}
                    parent={parent}
                    expanded={selectionState.expandedCategories.has(parent.id)}
                    onToggle={() => toggleCategoryExpansion(parent.id)}
                    childrenContent={parent.children.map((child) => renderIndustryOption({ ...child, children: [] }, true))}
                  >
                  </ParentIndustryAccordion>
                ))}
              </RadioGroup>
            </div>
          </div>
        )}

        {(mode === 'multi' || mode === 'both') && (
          <div>
            <Label className="text-sm font-medium">{multiSelectLabel}</Label>
            <div className="mt-2 space-y-4">
              {industries.map((parent) => (
                <ParentIndustryAccordion
                  key={parent.id}
                  parent={parent}
                  expanded={selectionState.expandedCategories.has(parent.id)}
                  onToggle={() => toggleCategoryExpansion(parent.id)}
                  childrenContent={parent.children.map((child) => renderIndustryOption({ ...child, children: [] }, true))}
                >
                </ParentIndustryAccordion>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
