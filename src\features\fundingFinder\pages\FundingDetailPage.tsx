import React, { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { 
  ArrowLeft, 
  Calendar, 
  ExternalLink, 
  Building2, 
  Users, 
  Heart,
  HandHeart,
  MapPin,
  Briefcase,
  User
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { supabase } from '@/integrations/supabase/client'
import { FundingOpportunityService } from '../services/fundingOpportunityService'
import type { FundingOpportunityWithInterests } from '../types'
import { FUNDING_TYPES } from '../types'

interface CollaboratorProfile {
  id: string
  first_name: string | null
  last_name: string | null
  job_title: string | null
  organization: string | null
  avatar_url: string | null
  location?: {
    name: string
    path: string
  } | null
  is_interested: boolean
  wants_collaboration: boolean
  note: string | null
}

const FundingDetailPage = () => {
  const { fundingId } = useParams<{ fundingId: string }>()
  const navigate = useNavigate()
  const { toast } = useToast()
  
  const [opportunity, setOpportunity] = useState<FundingOpportunityWithInterests | null>(null)
  const [collaborators, setCollaborators] = useState<CollaboratorProfile[]>([])
  const [loading, setLoading] = useState(true)
  const [isUpdatingInterest, setIsUpdatingInterest] = useState(false)
  const [showCollaborateDialog, setShowCollaborateDialog] = useState(false)

  useEffect(() => {
    if (!fundingId) return
    
    const loadFundingDetails = async () => {
      try {
        setLoading(true)
        
        // Load the funding opportunity with interests
        const fundingData = await FundingOpportunityService.getFundingOpportunityById(fundingId)
        
        if (!fundingData) {
          toast({
            title: "Not Found",
            description: "Funding opportunity not found",
            variant: "destructive"
          })
          navigate('/funding')
          return
        }

        setOpportunity(fundingData)

        // Load collaborator profiles (users who are interested or want collaboration)
        if (fundingData.interests && fundingData.interests.length > 0) {
          const collaboratorProfiles = await Promise.all(
            fundingData.interests
              .filter(interest => interest.is_interested || interest.wants_collaboration)
              .map(async (interest) => {
                try {
                  // Get user profile data
                  const { data: profileData, error } = await (supabase as any)
                    .from('profiles')
                    .select(`
                      id,
                      first_name,
                      last_name,
                      job_title,
                      organisation_name,
                      avatar_url,
                      profile_visible,
                      location_id
                    `)
                    .eq('id', interest.user_id)
                    .eq('profile_visible', true)
                    .single()

                  if (error || !profileData) return null

                  // Get location if available
                  let location = null
                  if (profileData.location_id) {
                    try {
                      const { data: locationData } = await (supabase as any)
                        .rpc('get_location_path', { location_id: profileData.location_id })
                      
                      const { data: locationInfo } = await (supabase as any)
                        .from('locations')
                        .select('name')
                        .eq('id', profileData.location_id)
                        .single()

                      if (locationData && locationInfo) {
                        location = {
                          name: locationInfo.name,
                          path: locationData
                        }
                      }
                    } catch (err) {
                      console.error('Error fetching location:', err)
                    }
                  }

                  return {
                    id: profileData.id,
                    first_name: profileData.first_name,
                    last_name: profileData.last_name,
                    job_title: profileData.job_title,
                    organization: profileData.organisation_name,
                    avatar_url: profileData.avatar_url,
                    location,
                    is_interested: interest.is_interested,
                    wants_collaboration: interest.wants_collaboration,
                    note: interest.note
                  }
                } catch (error) {
                  console.error('Error fetching collaborator profile:', error)
                  return null
                }
              })
          )

          setCollaborators(collaboratorProfiles.filter(Boolean) as CollaboratorProfile[])
        }
      } catch (error) {
        console.error('Error loading funding details:', error)
        toast({
          title: "Error",
          description: "Failed to load funding opportunity details",
          variant: "destructive"
        })
        navigate('/funding')
      } finally {
        setLoading(false)
      }
    }

    loadFundingDetails()
  }, [fundingId, navigate, toast])

  // Helper function to reload collaborators data
  const reloadCollaborators = async (opportunityId: string) => {
    try {
      const updatedFunding = await FundingOpportunityService.getFundingOpportunityById(opportunityId)
      if (updatedFunding?.interests) {
        const collaboratorProfiles = await Promise.all(
          updatedFunding.interests
            .filter(interest => interest.is_interested || interest.wants_collaboration)
            .map(async (interest) => {
              try {
                // Get user profile data
                const { data: profileData, error } = await (supabase as any)
                  .from('profiles')
                  .select(`
                    id,
                    first_name,
                    last_name,
                    job_title,
                    organisation_name,
                    avatar_url,
                    profile_visible,
                    location_id
                  `)
                  .eq('id', interest.user_id)
                  .eq('profile_visible', true)
                  .single()

                if (error || !profileData) return null

                // Get location if available
                let location = null
                if (profileData.location_id) {
                  try {
                    const { data: locationData } = await (supabase as any)
                      .rpc('get_location_path', { location_id: profileData.location_id })
                    
                    const { data: locationInfo } = await (supabase as any)
                      .from('locations')
                      .select('name')
                      .eq('id', profileData.location_id)
                      .single()

                    if (locationData && locationInfo) {
                      location = {
                        name: locationInfo.name,
                        path: locationData
                      }
                    }
                  } catch (err) {
                    console.error('Error fetching location:', err)
                  }
                }

                return {
                  id: profileData.id,
                  first_name: profileData.first_name,
                  last_name: profileData.last_name,
                  job_title: profileData.job_title,
                  organization: profileData.organisation_name,
                  avatar_url: profileData.avatar_url,
                  location,
                  is_interested: interest.is_interested,
                  wants_collaboration: interest.wants_collaboration,
                  note: interest.note
                }
              } catch (error) {
                console.error('Error fetching collaborator profile:', error)
                return null
              }
            })
        )

        setCollaborators(collaboratorProfiles.filter(Boolean) as CollaboratorProfile[])
        
        // Update the opportunity with new interests data
        setOpportunity(prev => prev ? {
          ...prev,
          interests: updatedFunding.interests
        } : null)
      }
    } catch (error) {
      console.error('Error reloading collaborators:', error)
    }
  }

  const handleCollaborationToggle = async () => {
    if (!opportunity) return
    
    setIsUpdatingInterest(true)
    
    try {
      if (!wantsCollaboration) {
        // Set collaboration to true, keep current interest status
        await FundingOpportunityService.setUserInterest(
          opportunity.id,
          isUserInterested,
          true
        )
        
        // Update local state
        setOpportunity(prev => prev ? {
          ...prev,
          user_interest: {
            ...prev.user_interest,
            is_interested: isUserInterested,
            wants_collaboration: true
          }
        } : null)
        
        toast({
          title: "Collaboration Updated",
          description: "You've indicated you want to collaborate on this opportunity"
        })
      } else {
        // Remove collaboration but keep interest if it exists
        if (isUserInterested) {
          await FundingOpportunityService.setUserInterest(
            opportunity.id,
            true,
            false
          )
          
          // Update local state
          setOpportunity(prev => prev ? {
            ...prev,
            user_interest: {
              ...prev.user_interest,
              is_interested: true,
              wants_collaboration: false
            }
          } : null)
        } else {
          await FundingOpportunityService.removeUserInterest(opportunity.id)
          
          // Update local state
          setOpportunity(prev => prev ? {
            ...prev,
            user_interest: null
          } : null)
        }
        toast({
          title: "Collaboration Removed",
          description: "You're no longer looking to collaborate on this opportunity"
        })
      }
      
      // Reload collaborators data to reflect changes
      await reloadCollaborators(opportunity.id)
    } catch (error) {
      console.error('Error updating collaboration:', error)
      toast({
        title: "Error",
        description: "Failed to update your collaboration status. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsUpdatingInterest(false)
    }
  }

  const handleInterestToggle = async () => {
    if (!opportunity) return
    
    setIsUpdatingInterest(true)
    
    try {
      if (!isUserInterested) {
        // Set interest to true, keep current collaboration status
        await FundingOpportunityService.setUserInterest(
          opportunity.id,
          true,
          wantsCollaboration
        )
        
        // Update local state
        setOpportunity(prev => prev ? {
          ...prev,
          user_interest: {
            ...prev.user_interest,
            is_interested: true,
            wants_collaboration: wantsCollaboration
          }
        } : null)
        
        toast({
          title: "Bookmarked",
          description: "This opportunity has been saved to your profile"
        })
      } else {
        // Remove interest but keep collaboration if it exists
        if (wantsCollaboration) {
          await FundingOpportunityService.setUserInterest(
            opportunity.id,
            false,
            true
          )
          
          // Update local state
          setOpportunity(prev => prev ? {
            ...prev,
            user_interest: {
              ...prev.user_interest,
              is_interested: false,
              wants_collaboration: true
            }
          } : null)
        } else {
          await FundingOpportunityService.removeUserInterest(opportunity.id)
          
          // Update local state
          setOpportunity(prev => prev ? {
            ...prev,
            user_interest: null
          } : null)
        }
        toast({
          title: "Bookmark Removed",
          description: "This opportunity has been removed from your profile"
        })
      }
      
      // Reload collaborators data to reflect changes
      await reloadCollaborators(opportunity.id)
    } catch (error) {
      console.error('Error updating interest:', error)
      toast({
        title: "Error",
        description: "Failed to update your bookmark. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsUpdatingInterest(false)
    }
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return null
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    })
  }

  const formatAmount = (amount: number | null) => {
    if (!amount) return null
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const getAmountDisplay = () => {
    if (!opportunity) return null
    
    if (opportunity.amount_min && opportunity.amount_max) {
      return `${formatAmount(opportunity.amount_min)} - ${formatAmount(opportunity.amount_max)}`
    }
    if (opportunity.amount_min) {
      return `From ${formatAmount(opportunity.amount_min)}`
    }
    if (opportunity.amount_max) {
      return `Up to ${formatAmount(opportunity.amount_max)}`
    }
    return null
  }

  const isDeadlinePassed = () => {
    if (!opportunity?.deadline_date) return false
    const deadline = new Date(opportunity.deadline_date)
    const now = new Date()
    return deadline < now
  }

  const isDeadlineSoon = () => {
    if (!opportunity?.deadline_date) return false
    const deadline = new Date(opportunity.deadline_date)
    const now = new Date()
    const daysUntilDeadline = Math.ceil((deadline.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    return daysUntilDeadline <= 7 && daysUntilDeadline >= 0
  }

  const getLastLocationPart = (locationPath: string) => {
    // Extract the last part of a location path like "England > Yorkshire and the Humber > East Riding of Yorkshire"
    const parts = locationPath.split(' > ')
    return parts[parts.length - 1].trim()
  }

  const fundingTypeInfo = FUNDING_TYPES.find(type => type.value === opportunity?.funding_type)
  const userInterest = opportunity?.user_interest
  const isUserInterested = userInterest?.is_interested || false
  const wantsCollaboration = userInterest?.wants_collaboration || false

  // Get counts from raw interest data (includes all users, not just visible profiles)
  const totalInterestedCount = opportunity?.interests?.filter(i => i.is_interested).length || 0
  const totalCollaborationCount = opportunity?.interests?.filter(i => i.wants_collaboration).length || 0
  
  // Get visible users for display
  const collaborationUsers = collaborators.filter(c => c.wants_collaboration)

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid gap-6">
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-48 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!opportunity) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Funding Opportunity Not Found</h1>
          <Button onClick={() => navigate('/funding')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Funding Finder
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header */}
      <div className="mb-6">
        <Button 
          variant="ghost" 
          onClick={() => navigate('/funding')}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Funding Finder
        </Button>
        
        {/* Mobile-first responsive header */}
        <div className="space-y-4">
          {/* Title and organization */}
          <div className="space-y-2">
            <h1 className="text-2xl sm:text-3xl font-bold leading-tight">{opportunity.name}</h1>
            <p className="text-lg sm:text-xl text-muted-foreground">{opportunity.organization_name}</p>
            
            <div className="flex flex-wrap gap-2">
              <Badge variant={isDeadlinePassed() ? "destructive" : "secondary"}>
                {fundingTypeInfo?.label || opportunity.funding_type}
              </Badge>
              
              {isDeadlineSoon() && !isDeadlinePassed() && (
                <Badge variant="destructive">
                  Deadline Soon
                </Badge>
              )}
              
              {isDeadlinePassed() && (
                <Badge variant="destructive">
                  Deadline Passed
                </Badge>
              )}
            </div>
          </div>
          
          {/* Action Buttons - Stack on mobile, horizontal on larger screens */}
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            {opportunity.url && (
              <Button variant="outline" asChild className="w-full sm:w-auto">
                <a
                  href={opportunity.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-center gap-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  <span className="hidden sm:inline">More Info</span>
                  <span className="sm:hidden">More Info</span>
                </a>
              </Button>
            )}

            <Button
              variant={isUserInterested ? "default" : "outline"}
              onClick={handleInterestToggle}
              disabled={isUpdatingInterest || isDeadlinePassed()}
              className="flex items-center justify-center gap-2 w-full sm:w-auto"
            >
              <Heart className={`h-4 w-4 ${isUserInterested ? 'fill-current' : ''}`} />
              <span className="hidden sm:inline">{isUserInterested ? 'Bookmarked' : 'Bookmark'}</span>
              <span className="sm:hidden">{isUserInterested ? 'Saved' : 'Save'}</span>
            </Button>

            {wantsCollaboration ? (
              <Button
                variant="default"
                onClick={handleCollaborationToggle}
                disabled={isUpdatingInterest || isDeadlinePassed()}
                className="flex items-center justify-center gap-2 w-full sm:w-auto"
              >
                <HandHeart className="h-4 w-4 fill-current" />
                <span className="hidden sm:inline">Stop Collaborating</span>
                <span className="sm:hidden">Stop</span>
              </Button>
            ) : (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="outline"
                    disabled={isUpdatingInterest || isDeadlinePassed()}
                    className="flex items-center justify-center gap-2 w-full sm:w-auto"
                  >
                    <HandHeart className="h-4 w-4" />
                    <span className="hidden sm:inline">Collaborate</span>
                    <span className="sm:hidden">Collaborate</span>
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Collaborate on this Funding Opportunity</AlertDialogTitle>
                    <AlertDialogDescription>
                      By clicking "Collaborate" you are not applying for the funding - you are just showing your potential interest to collaborate with others on this opportunity.
                      <br /><br />
                      Please use the "More Info" button to find details on how to apply for the funding.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleCollaborationToggle}>
                      Continue to Collaborate
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Description */}
          {opportunity.description && (
            <Card>
              <CardHeader>
                <CardTitle>Description</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose max-w-none">
                  <p className="whitespace-pre-wrap">{opportunity.description}</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Apply for this Funding */}
          {opportunity.url && (
            <Card>
              <CardHeader>
                <CardTitle>Apply for this Funding</CardTitle>
              </CardHeader>
              <CardContent>
                <Button variant="default" asChild className="w-full sm:w-auto">
                  <a
                    href={opportunity.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-center gap-2"
                  >
                    <ExternalLink className="h-4 w-4" />
                    More Info
                  </a>
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Eligibility Criteria */}
          {opportunity.eligibility_criteria && (
            <Card>
              <CardHeader>
                <CardTitle>Eligibility Criteria</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose max-w-none">
                  <p className="whitespace-pre-wrap">{opportunity.eligibility_criteria}</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Collaboration Section */}
          {totalCollaborationCount > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Looking to Collaborate ({totalCollaborationCount} total, {collaborationUsers.length} visible)
                </CardTitle>
                <CardDescription>
                  Connect with other professionals interested in collaborating on this opportunity
                </CardDescription>
              </CardHeader>
              <CardContent>
                {collaborationUsers.length > 0 ? (
                  <div className="grid gap-4">
                    {collaborationUsers.map((collaborator) => (
                      <Card key={collaborator.id} className="transition-all hover:shadow-md">
                        <CardContent className="p-3 sm:p-4">
                          <Link
                            to={`/members/${collaborator.id}`}
                            className="block"
                          >
                            <div className="flex items-start gap-3">
                              <Avatar className="h-10 w-10 sm:h-12 sm:w-12 flex-shrink-0">
                                <AvatarImage src={collaborator.avatar_url || undefined} />
                                <AvatarFallback className="text-xs sm:text-sm">
                                  {collaborator.first_name?.[0]}{collaborator.last_name?.[0]}
                                </AvatarFallback>
                              </Avatar>
                              
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                  <h4 className="font-medium text-primary hover:underline text-sm sm:text-base">
                                    {collaborator.first_name} {collaborator.last_name}
                                  </h4>
                                  <ExternalLink className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                                </div>
                                
                                {collaborator.job_title && (
                                  <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
                                    <Briefcase className="h-3 w-3 flex-shrink-0" />
                                    <span className="truncate">{collaborator.job_title}</span>
                                  </div>
                                )}
                                
                                {collaborator.organization && (
                                  <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
                                    <Building2 className="h-3 w-3 flex-shrink-0" />
                                    <span className="truncate">{collaborator.organization}</span>
                                  </div>
                                )}
                                
                                {collaborator.location && (
                                  <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
                                    <MapPin className="h-3 w-3 flex-shrink-0" />
                                    <span className="truncate">{getLastLocationPart(collaborator.location.path)}</span>
                                  </div>
                                )}
                                
                                {collaborator.wants_collaboration && (
                                  <div className="flex gap-1 mt-2">
                                    <Badge variant="default" className="text-xs">
                                      <HandHeart className="h-3 w-3 mr-1" />
                                      Collaborating
                                    </Badge>
                                  </div>
                                )}
                                
                                {collaborator.note && (
                                  <p className="text-sm text-muted-foreground mt-2 italic bg-muted/50 p-2 rounded">
                                    "{collaborator.note}"
                                  </p>
                                )}
                              </div>
                            </div>
                          </Link>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p className="text-sm font-medium mb-2">
                      {totalCollaborationCount} {totalCollaborationCount === 1 ? 'person wants' : 'people want'} to collaborate!
                    </p>
                    <p className="text-sm">
                      However, {totalCollaborationCount === 1 ? 'their profile is' : 'their profiles are'} currently private and can't be displayed.
                    </p>
                    <div className="mt-4 p-3 bg-blue-50 rounded-lg text-left max-w-md mx-auto">
                      <p className="text-xs text-blue-800 font-medium mb-1">💡 For collaborators:</p>
                      <p className="text-xs text-blue-700">
                        To connect with others, go to your Profile → Account Settings → Make your profile visible to other members.
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Key Details */}
          <Card>
            <CardHeader>
              <CardTitle>Key Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Amount */}
              {getAmountDisplay() && (
                <div>
                  <h4 className="font-medium mb-1">Funding Amount</h4>
                  <p className="text-lg font-semibold text-green-600">{getAmountDisplay()}</p>
                </div>
              )}

              {/* Dates */}
              <div className="space-y-3">
                {opportunity.date_listed && (
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">Listed:</span>
                    <span>{formatDate(opportunity.date_listed)}</span>
                  </div>
                )}
                
                {opportunity.deadline_date && (
                  <div className={`flex items-center gap-2 text-sm ${
                    isDeadlinePassed() ? 'text-destructive' : 
                    isDeadlineSoon() ? 'text-orange-600' : ''
                  }`}>
                    <Calendar className="h-4 w-4" />
                    <span className="text-muted-foreground">Deadline:</span>
                    <span>
                      {formatDate(opportunity.deadline_date)}
                      {isDeadlinePassed() && ' (Passed)'}
                    </span>
                  </div>
                )}
              </div>

              {/* Creator */}
              {opportunity.creator && (
                <div>
                  <h4 className="font-medium mb-2">Added by</h4>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={opportunity.creator.avatar_url || undefined} />
                      <AvatarFallback className="text-xs">
                        {opportunity.creator.first_name?.[0]}{opportunity.creator.last_name?.[0]}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-sm">
                      {opportunity.creator.first_name} {opportunity.creator.last_name}
                    </span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Interest Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Community Interest</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <HandHeart className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Want to Collaborate</span>
                </div>
                <Badge variant="secondary">{totalCollaborationCount}</Badge>
              </div>
              
              {totalCollaborationCount > 0 && collaborationUsers.length === 0 && (
                <div className="text-xs text-muted-foreground p-2 bg-muted/30 rounded">
                  <p>Some users want to collaborate but have private profiles.</p>
                </div>
              )}
            </CardContent>
          </Card>


        </div>
      </div>
    </div>
  )
}

export default FundingDetailPage 