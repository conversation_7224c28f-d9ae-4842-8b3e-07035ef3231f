import React from 'react';
import { 
  Zap, 
  Car, 
  Building, 
  Factory, 
  TreePine, 
  BarChart3, 
  Target,
  LucideIcon
} from 'lucide-react';

export interface NetZeroCategoryTheme {
  icon: LucideIcon;
  bgColor: string;
  textColor: string;
  borderColor: string;
  hoverBgColor: string;
  name: string;
}

// Net-zero category theme mapping based on category names
export const NET_ZERO_CATEGORY_THEMES: Record<string, NetZeroCategoryTheme> = {
  'Energy': {
    icon: Zap,
    bgColor: '',
    textColor: 'text-yellow-700',
    borderColor: 'border-yellow-200',
    hoverBgColor: 'hover:bg-gray-50',
    name: 'Energy'
  },
  'Transportation': {
    icon: Car,
    bgColor: '',
    textColor: 'text-blue-700',
    borderColor: 'border-blue-200',
    hoverBgColor: 'hover:bg-gray-50',
    name: 'Transportation'
  },
  'Buildings and Facilities': {
    icon: Building,
    bgColor: '',
    textColor: 'text-orange-700',
    borderColor: 'border-orange-200',
    hoverBgColor: 'hover:bg-gray-50',
    name: 'Buildings and Facilities'
  },
  'Manufacturing and Operations': {
    icon: Factory,
    bgColor: '',
    textColor: 'text-purple-700',
    borderColor: 'border-purple-200',
    hoverBgColor: 'hover:bg-gray-50',
    name: 'Manufacturing and Operations'
  },
  'Agriculture and Land Use': {
    icon: TreePine,
    bgColor: '',
    textColor: 'text-green-700',
    borderColor: 'border-green-200',
    hoverBgColor: 'hover:bg-gray-50',
    name: 'Agriculture and Land Use'
  },
  'Carbon Management': {
    icon: BarChart3,
    bgColor: '',
    textColor: 'text-gray-700',
    borderColor: 'border-gray-200',
    hoverBgColor: 'hover:bg-gray-50',
    name: 'Carbon Management'
  },
  'Strategy, Finance and Governance': {
    icon: Target,
    bgColor: '',
    textColor: 'text-indigo-700',
    borderColor: 'border-indigo-200',
    hoverBgColor: 'hover:bg-gray-50',
    name: 'Strategy, Finance and Governance'
  }
};

// Helper function to get theme for a net-zero category
export const getNetZeroCategoryTheme = (categoryName: string): NetZeroCategoryTheme => {
  return NET_ZERO_CATEGORY_THEMES[categoryName] || {
    icon: Target,
    bgColor: '',
    textColor: 'text-emerald-700',
    borderColor: 'border-emerald-200',
    hoverBgColor: 'hover:bg-gray-50',
    name: categoryName
  };
};

// Helper function to get theme by subcategory ID (requires category data lookup)
export const getNetZeroCategoryThemeById = (categories: any[], subcategoryId: string): NetZeroCategoryTheme => {
  // Find the subcategory and its parent category
  for (const category of categories) {
    for (const subcategory of category.subcategories || []) {
      if (subcategory.id === subcategoryId) {
        return getNetZeroCategoryTheme(category.name);
      }
    }
  }
  
  // Fallback theme
  return {
    icon: Target,
    bgColor: '',
    textColor: 'text-emerald-700',
    borderColor: 'border-emerald-200',
    hoverBgColor: 'hover:bg-gray-50',
    name: 'Unknown'
  };
};

// Component to render a net-zero category icon with theme
export const NetZeroCategoryIcon: React.FC<{
  categoryName: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}> = ({ categoryName, size = 'lg', className = '' }) => {
  const theme = getNetZeroCategoryTheme(categoryName);
  const Icon = theme.icon;
  
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-8 h-8'
  };
  
  return (
    <Icon className={`${sizeClasses[size]} ${theme.textColor} ${className}`} />
  );
}; 