import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Leaf, X } from 'lucide-react';
import { NetZeroCategoryService } from '@/services/netZeroCategoryService';
import { getNetZeroCategoryTheme, NetZeroCategoryIcon } from '@/utils/netZeroCategoryIcons';
import type { 
  NetZeroCategoryWithSubcategories, 
  CategorySelectionState 
} from '@/types/netzero-categories.types';

interface NetZeroCategorySelectorProps {
  selectedSubcategories: string[];
  onSelectionChange: (subcategoryIds: string[]) => void;
  primarySubcategoryId?: string;
  onPrimaryChange?: (subcategoryId: string | undefined) => void;
  allowPrimarySelection?: boolean;
  maxSelections?: number;
  title?: string;
  description?: string;
  className?: string;
}

// Category icons are now handled by the NetZeroCategoryIcon component

export const NetZeroCategorySelector: React.FC<NetZeroCategorySelectorProps> = ({
  selectedSubcategories,
  onSelectionChange,
  primarySubcategoryId,
  onPrimaryChange,
  allowPrimarySelection = false,
  maxSelections,
  title = "Net-Zero Categories",
  description = "Select the categories that best represent your interests or business focus",
  className = ""
}) => {
  const [categories, setCategories] = useState<NetZeroCategoryWithSubcategories[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectionState, setSelectionState] = useState<CategorySelectionState>({
    selectedSubcategories: new Set(selectedSubcategories),
    expandedCategories: new Set()
  });

  useEffect(() => {
    loadCategories();
  }, []);

  useEffect(() => {
    setSelectionState(prev => ({
      ...prev,
      selectedSubcategories: new Set(selectedSubcategories)
    }));
  }, [selectedSubcategories]);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const data = await NetZeroCategoryService.getAllCategoriesWithSubcategories();
      setCategories(data);
      
      // Auto-expand categories that have selected subcategories
      const expandedCategories = new Set<string>();
      data.forEach(category => {
        if (category.subcategories.some(sub => selectedSubcategories.includes(sub.id))) {
          expandedCategories.add(category.id);
        }
      });
      
      setSelectionState(prev => ({
        ...prev,
        expandedCategories
      }));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load categories');
    } finally {
      setLoading(false);
    }
  };

  const handleSubcategoryToggle = (subcategoryId: string) => {
    const newSelected = new Set(selectionState.selectedSubcategories);
    
    if (newSelected.has(subcategoryId)) {
      newSelected.delete(subcategoryId);
      // If this was the primary, clear primary selection
      if (primarySubcategoryId === subcategoryId && onPrimaryChange) {
        onPrimaryChange(undefined);
      }
    } else {
      // Check max selections limit
      if (maxSelections && newSelected.size >= maxSelections) {
        return; // Don't allow more selections
      }
      newSelected.add(subcategoryId);
    }
    
    const newSelectedArray = Array.from(newSelected);
    setSelectionState(prev => ({
      ...prev,
      selectedSubcategories: newSelected
    }));
    onSelectionChange(newSelectedArray);
  };

  const handlePrimaryToggle = (subcategoryId: string) => {
    if (!onPrimaryChange) return;
    
    if (primarySubcategoryId === subcategoryId) {
      onPrimaryChange(undefined);
    } else {
      // Ensure the subcategory is selected first
      if (!selectionState.selectedSubcategories.has(subcategoryId)) {
        handleSubcategoryToggle(subcategoryId);
      }
      onPrimaryChange(subcategoryId);
    }
  };

  const toggleCategoryExpansion = (categoryId: string) => {
    setSelectionState(prev => {
      const newExpanded = new Set(prev.expandedCategories);
      if (newExpanded.has(categoryId)) {
        newExpanded.delete(categoryId);
      } else {
        newExpanded.add(categoryId);
      }
      return {
        ...prev,
        expandedCategories: newExpanded
      };
    });
  };

  const getSelectedCount = () => selectionState.selectedSubcategories.size;

  const clearAllSelections = () => {
    setSelectionState(prev => ({
      ...prev,
      selectedSubcategories: new Set()
    }));
    onSelectionChange([]);
    if (onPrimaryChange) {
      onPrimaryChange(undefined);
    }
  };

  const getSubcategoryDisplayName = (subcategoryId: string) => {
    for (const category of categories) {
      const subcategory = category.subcategories.find(sub => sub.id === subcategoryId);
      if (subcategory) {
        return subcategory.name;
      }
    }
    return subcategoryId;
  };

  const getTotalSubcategories = () => {
    return categories.reduce((total, category) => total + category.subcategories.length, 0);
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Leaf className="w-5 h-5 text-green-600" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="py-8">
            <p className="text-muted-foreground text-left">Loading categories...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Leaf className="w-5 h-5 text-green-600" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="py-8">
            <p className="text-red-600 text-left">Error: {error}</p>
            <Button onClick={loadCategories} variant="outline" className="mt-4">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Leaf className="w-5 h-5 text-green-600" />
          {title}
        </CardTitle>
        <CardDescription className="text-left">
          {description}
          {maxSelections && (
            <span className="block mt-1 text-sm">
              Selected: {getSelectedCount()}/{maxSelections}
            </span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Selected Items Summary */}
        {getSelectedCount() > 0 && (
          <div className="sticky top-20 z-10 mb-6 p-4 bg-green-50 rounded-lg border border-green-200 shadow-md">
            <h3 className="font-semibold text-green-900 mb-2">
              Selected Net-Zero Categories ({getSelectedCount()}):
            </h3>
            <div className="flex flex-wrap gap-2 max-h-40 overflow-y-auto">
              {Array.from(selectionState.selectedSubcategories).map((subcategoryId) => {
                const displayName = getSubcategoryDisplayName(subcategoryId);
                const isPrimary = primarySubcategoryId === subcategoryId;
                
                // Find the category for this subcategory to get the theme
                const category = categories.find(cat => 
                  cat.subcategories.some(sub => sub.id === subcategoryId)
                );
                const theme = category ? getNetZeroCategoryTheme(category.name) : null;
                
                return (
                  <span 
                    key={subcategoryId}
                    className={`inline-flex items-center px-3 py-1 text-xs font-medium rounded-full border ${
                      isPrimary 
                        ? 'bg-green-600 text-white border-green-600' 
                        : theme ? `border-gray-300 ${theme.textColor}` : 'border-gray-300 text-emerald-800'
                    }`}
                  >
                    {theme && !isPrimary && (
                      <NetZeroCategoryIcon categoryName={category!.name} size="sm" className="w-3 h-3 mr-1" />
                    )}
                    {displayName}
                    {isPrimary && <span className="ml-1 text-xs">(Primary)</span>}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSubcategoryToggle(subcategoryId);
                      }}
                      className={`ml-2 hover:rounded-full p-0.5 ${
                        isPrimary 
                          ? 'text-white hover:bg-green-700' 
                          : theme ? `${theme.textColor} hover:bg-opacity-80` : 'text-emerald-600 hover:text-emerald-800 hover:bg-emerald-200'
                      }`}
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                );
              })}
            </div>
            <div className="mt-3 flex space-x-4">
              <button
                onClick={clearAllSelections}
                className="text-sm text-green-600 hover:text-green-800 underline"
              >
                Clear all selections
              </button>
              <span className="text-sm text-gray-500">
                Selected: {getSelectedCount()} / {getTotalSubcategories()} total
              </span>
            </div>
          </div>
        )}

        {categories.map((category) => {
          const isExpanded = selectionState.expandedCategories.has(category.id);
          const selectedInCategory = category.subcategories.filter(sub => 
            selectionState.selectedSubcategories.has(sub.id)
          ).length;
          
          return (
            <Collapsible
              key={category.id}
              open={isExpanded}
              onOpenChange={() => toggleCategoryExpansion(category.id)}
            >
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className={`w-full justify-between p-4 h-auto border rounded-lg ${getNetZeroCategoryTheme(category.name).borderColor} ${getNetZeroCategoryTheme(category.name).hoverBgColor}`}
                >
                  <div className="flex items-center gap-3">
                    {isExpanded ? (
                      <ChevronDown className="w-4 h-4" />
                    ) : (
                      <ChevronRight className="w-4 h-4" />
                    )}
                    <div className="flex-1 min-w-0">
                      <h3 className={`font-bold break-words whitespace-normal text-left flex items-center gap-2 ${getNetZeroCategoryTheme(category.name).textColor}`}>
                        <NetZeroCategoryIcon categoryName={category.name} size="xl" />
                        {category.name}
                      </h3>
                      {category.description && (
                        <p className="text-sm text-muted-foreground mt-1 break-words whitespace-normal text-left">
                          {category.description}
                        </p>
                      )}
                    </div>
                  </div>
                  {selectedInCategory > 0 && (
                    <Badge variant="secondary" className="shrink-0 ml-2">
                      {selectedInCategory} selected
                    </Badge>
                  )}
                </Button>
              </CollapsibleTrigger>
              
              <CollapsibleContent className="mt-2 ml-4 space-y-2">
                {category.subcategories.map((subcategory) => {
                  const isSelected = selectionState.selectedSubcategories.has(subcategory.id);
                  const isPrimary = primarySubcategoryId === subcategory.id;
                  const isMaxReached = maxSelections && getSelectedCount() >= maxSelections && !isSelected;
                  
                  return (
                    <div
                      key={subcategory.id}
                      className="flex items-start gap-3 p-3 rounded-lg border bg-card hover:bg-muted/30"
                    >
                      <Checkbox
                        id={subcategory.id}
                        checked={isSelected}
                        onCheckedChange={() => handleSubcategoryToggle(subcategory.id)}
                        disabled={isMaxReached}
                      />
                      <div className="flex-1 min-w-0">
                        <label
                          htmlFor={subcategory.id}
                          className="text-sm font-medium cursor-pointer"
                        >
                          {subcategory.name}
                        </label>
                        {subcategory.description && (
                          <p className="text-xs text-muted-foreground mt-1">
                            {subcategory.description}
                          </p>
                        )}
                      </div>
                      {allowPrimarySelection && isSelected && (
                        <Button
                          size="sm"
                          variant={isPrimary ? "default" : "outline"}
                          onClick={() => handlePrimaryToggle(subcategory.id)}
                          className="text-xs"
                        >
                          {isPrimary ? "Primary" : "Set Primary"}
                        </Button>
                      )}
                    </div>
                  );
                })}
              </CollapsibleContent>
            </Collapsible>
          );
        })}
      </CardContent>
    </Card>
  );
};
