import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'
import { Plus, Edit, Trash2, ExternalLink, Calendar, Users, Heart, HandHeart } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/contexts/AuthContext'
import { FundingOpportunityService } from '@/features/fundingFinder/services/fundingOpportunityService'
import { AddFundingOpportunityForm } from '@/features/fundingFinder/components'
import EditFundingOpportunityForm from '@/features/fundingFinder/components/EditFundingOpportunityForm'
import type { FundingOpportunityWithInterests } from '@/features/fundingFinder/types'
import { FUNDING_TYPES, CURRENCY_OPTIONS } from '@/features/fundingFinder/types'

const FundingManagement = () => {
  const [myOpportunities, setMyOpportunities] = useState<FundingOpportunityWithInterests[]>([])
  const [interestedOpportunities, setInterestedOpportunities] = useState<FundingOpportunityWithInterests[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingOpportunity, setEditingOpportunity] = useState<FundingOpportunityWithInterests | null>(null)
  const [updatingInterests, setUpdatingInterests] = useState<Set<string>>(new Set())
  const { user } = useAuth()
  const { toast } = useToast()
  const navigate = useNavigate()

  const loadMyFunding = async () => {
    if (!user) return

    try {
      setLoading(true)
      
      // Get opportunities created by the user
      const myFundingResponse = await FundingOpportunityService.getFundingOpportunities(
        {},
        { field: 'date_listed', direction: 'desc' },
        1,
        100
      )
      
      const myFunding = myFundingResponse.data.filter(opp => opp.created_by_user_id === user.id)
      
      // Get opportunities the user is interested in
      const allOpportunities = await FundingOpportunityService.getFundingOpportunities(
        {},
        { field: 'date_listed', direction: 'desc' },
        1,
        100
      )
      
      // Get user interests for each opportunity
      const opportunitiesWithUserInterests = await Promise.all(
        allOpportunities.data.map(async (opportunity) => {
          try {
            const userInterest = await FundingOpportunityService.getUserInterest(opportunity.id)
            return {
              ...opportunity,
              user_interest: userInterest
            }
          } catch (error) {
            return opportunity
          }
        })
      )
      
      const interested = opportunitiesWithUserInterests.filter(opp => 
        opp.user_interest && (opp.user_interest.is_interested || opp.user_interest.wants_collaboration)
      )
      
      setMyOpportunities(myFunding)
      setInterestedOpportunities(interested)
    } catch (error) {
      console.error('Error loading funding data:', error)
      toast({
        title: "Error",
        description: "Failed to load funding opportunities",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadMyFunding()
  }, [user])

  const handleAddSuccess = () => {
    setShowAddForm(false)
    loadMyFunding()
    toast({
      title: "Success",
      description: "Funding opportunity added successfully!"
    })
  }

  const handleEditSuccess = () => {
    setEditingOpportunity(null)
    loadMyFunding()
    toast({
      title: "Success",
      description: "Funding opportunity updated successfully!"
    })
  }

  const handleDelete = async (opportunityId: string) => {
    if (!confirm('Are you sure you want to delete this funding opportunity?')) {
      return
    }

    try {
      await FundingOpportunityService.deleteFundingOpportunity(opportunityId)
      toast({
        title: "Success",
        description: "Funding opportunity deleted successfully"
      })
      loadMyFunding()
    } catch (error) {
      console.error('Error deleting funding opportunity:', error)
      toast({
        title: "Error",
        description: "Failed to delete funding opportunity",
        variant: "destructive"
      })
    }
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return null
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  const formatAmount = (amount: number | null, currency: string | null) => {
    if (!amount) return null
    const currencyInfo = CURRENCY_OPTIONS.find(curr => curr.value === currency)
    const symbol = currencyInfo?.symbol || '£'
    return `${symbol}${amount.toLocaleString()}`
  }

  const getAmountDisplay = (opportunity: FundingOpportunityWithInterests) => {
    if (opportunity.amount_min && opportunity.amount_max) {
      return `${formatAmount(opportunity.amount_min, opportunity.currency)} - ${formatAmount(opportunity.amount_max, opportunity.currency)}`
    } else if (opportunity.amount_min) {
      return `From ${formatAmount(opportunity.amount_min, opportunity.currency)}`
    } else if (opportunity.amount_max) {
      return `Up to ${formatAmount(opportunity.amount_max, opportunity.currency)}`
    }
    return null
  }

  const isDeadlinePassed = (deadlineDate: string | null) => {
    if (!deadlineDate) return false
    return new Date(deadlineDate) < new Date()
  }

  const handleInterestToggle = async (opportunityId: string, currentInterested: boolean, currentCollaboration: boolean) => {
    setUpdatingInterests(prev => new Set(prev.add(opportunityId)))
    
    try {
      if (currentInterested) {
        // Remove interest but keep collaboration if it exists
        if (currentCollaboration) {
          await FundingOpportunityService.setUserInterest(opportunityId, false, true)
        } else {
          await FundingOpportunityService.removeUserInterest(opportunityId)
        }
        toast({
          title: "Interest Updated",
          description: "Removed from your bookmarks"
        })
      } else {
        // Add interest, keep current collaboration status
        await FundingOpportunityService.setUserInterest(opportunityId, true, currentCollaboration)
        toast({
          title: "Interest Updated", 
          description: "Added to your bookmarks"
        })
      }
      
      // Reload the data
      loadMyFunding()
    } catch (error) {
      console.error('Error updating interest:', error)
      toast({
        title: "Error",
        description: "Failed to update interest status",
        variant: "destructive"
      })
    } finally {
      setUpdatingInterests(prev => {
        const newSet = new Set(prev)
        newSet.delete(opportunityId)
        return newSet
      })
    }
  }

  const handleCollaborationToggle = async (opportunityId: string, currentInterested: boolean, currentCollaboration: boolean) => {
    setUpdatingInterests(prev => new Set(prev.add(opportunityId)))
    
    try {
      if (currentCollaboration) {
        // Remove collaboration but keep interest if it exists
        if (currentInterested) {
          await FundingOpportunityService.setUserInterest(opportunityId, true, false)
        } else {
          await FundingOpportunityService.removeUserInterest(opportunityId)
        }
        toast({
          title: "Collaboration Updated",
          description: "No longer looking to collaborate"
        })
      } else {
        // Add collaboration, keep current interest status
        await FundingOpportunityService.setUserInterest(opportunityId, currentInterested, true)
        toast({
          title: "Collaboration Updated",
          description: "Now looking to collaborate"
        })
      }
      
      // Reload the data
      loadMyFunding()
    } catch (error) {
      console.error('Error updating collaboration:', error)
      toast({
        title: "Error",
        description: "Failed to update collaboration status",
        variant: "destructive"
      })
    } finally {
      setUpdatingInterests(prev => {
        const newSet = new Set(prev)
        newSet.delete(opportunityId)
        return newSet
      })
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <p className="text-muted-foreground">Loading your funding opportunities...</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Add Button */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Funding Management</CardTitle>
              <CardDescription>
                Manage funding opportunities you've added and track your interests
              </CardDescription>
            </div>
            
            <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add Funding
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <AddFundingOpportunityForm
                  onSuccess={handleAddSuccess}
                  onCancel={() => setShowAddForm(false)}
                />
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
      </Card>

      {/* My Funding Opportunities */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            My Funding Opportunities
            <Badge variant="secondary">{myOpportunities.length}</Badge>
          </CardTitle>
          <CardDescription>
            Funding opportunities you've added to the platform
          </CardDescription>
        </CardHeader>
        <CardContent>
          {myOpportunities.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">You haven't added any funding opportunities yet.</p>
              <Button onClick={() => setShowAddForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Opportunity
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {myOpportunities.map((opportunity) => {
                const fundingTypeInfo = FUNDING_TYPES.find(type => type.value === opportunity.funding_type)
                const isPassed = isDeadlinePassed(opportunity.deadline_date)
                
                return (
                  <div key={opportunity.id} className={`border rounded-lg p-4 ${isPassed ? 'opacity-75' : ''}`}>
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold">{opportunity.name}</h3>
                          <Badge variant={isPassed ? "destructive" : "secondary"}>
                            {fundingTypeInfo?.label || opportunity.funding_type}
                          </Badge>
                          {isPassed && (
                            <Badge variant="destructive" className="text-xs">
                              Expired
                            </Badge>
                          )}
                        </div>
                        
                        <p className="text-sm text-muted-foreground mb-2">
                          {opportunity.organization_name}
                        </p>
                        
                        {opportunity.description && (
                          <p className="text-sm mb-3 line-clamp-2">
                            {opportunity.description}
                          </p>
                        )}
                        
                        <div className="flex flex-wrap gap-4 text-xs text-muted-foreground">
                          {opportunity.date_listed && (
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              <span>Listed {formatDate(opportunity.date_listed)}</span>
                            </div>
                          )}
                          
                          {opportunity.deadline_date && (
                            <div className={`flex items-center gap-1 ${isPassed ? 'text-destructive' : ''}`}>
                              <Calendar className="h-3 w-3" />
                              <span>Deadline {formatDate(opportunity.deadline_date)}</span>
                            </div>
                          )}
                          
                          {getAmountDisplay(opportunity) && (
                            <span>{getAmountDisplay(opportunity)}</span>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Heart className="h-3 w-3" />
                            <span>{opportunity.interest_count || 0} interested</span>
                          </div>
                          
                          <div className="flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            <span>{opportunity.collaboration_count || 0} want to collaborate</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        {opportunity.url && (
                          <Button
                            variant="outline"
                            size="sm"
                            asChild
                          >
                            <a
                              href={opportunity.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-1"
                            >
                              <ExternalLink className="h-3 w-3" />
                            </a>
                          </Button>
                        )}
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEditingOpportunity(opportunity)}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(opportunity.id)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Interested Opportunities */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            My Interests
            <Badge variant="secondary">{interestedOpportunities.length}</Badge>
          </CardTitle>
          <CardDescription>
            Funding opportunities you've shown interest in or want to collaborate on
          </CardDescription>
        </CardHeader>
        <CardContent>
          {interestedOpportunities.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">You haven't shown interest in any funding opportunities yet.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {interestedOpportunities.map((opportunity) => {
                const fundingTypeInfo = FUNDING_TYPES.find(type => type.value === opportunity.funding_type)
                const isPassed = isDeadlinePassed(opportunity.deadline_date)
                const userInterest = opportunity.user_interest
                
                const isUpdating = updatingInterests.has(opportunity.id)
                
                return (
                  <div key={opportunity.id} className={`border rounded-lg ${isPassed ? 'opacity-75' : ''}`}>
                    {/* Clickable main area */}
                    <div 
                      className="p-4 cursor-pointer hover:bg-muted/50 transition-colors"
                      onClick={() => navigate(`/funding/${opportunity.id}`)}
                    >
                      <div className="flex items-start justify-between gap-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold hover:text-primary">{opportunity.name}</h3>
                            <Badge variant={isPassed ? "destructive" : "secondary"}>
                              {fundingTypeInfo?.label || opportunity.funding_type}
                            </Badge>
                          </div>
                          
                          <p className="text-sm text-muted-foreground mb-2">
                            {opportunity.organization_name}
                          </p>
                          
                          {opportunity.description && (
                            <p className="text-sm mb-3 line-clamp-2">
                              {opportunity.description}
                            </p>
                          )}
                          
                          <div className="flex flex-wrap gap-4 text-xs text-muted-foreground">
                            {opportunity.deadline_date && (
                              <div className={`flex items-center gap-1 ${isPassed ? 'text-destructive' : ''}`}>
                                <Calendar className="h-3 w-3" />
                                <span>Deadline {formatDate(opportunity.deadline_date)}</span>
                              </div>
                            )}
                            
                            {getAmountDisplay(opportunity) && (
                              <span>{getAmountDisplay(opportunity)}</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Action buttons section */}
                    <div className="px-4 pb-4 flex items-center gap-4">
                      <div className="flex gap-2">
                        {/* Interest Toggle Button */}
                        <Button
                          variant={userInterest?.is_interested ? "default" : "outline"}
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleInterestToggle(
                              opportunity.id, 
                              userInterest?.is_interested || false, 
                              userInterest?.wants_collaboration || false
                            )
                          }}
                          disabled={isUpdating || isPassed}
                          className="flex items-center gap-1"
                        >
                          <Heart className={`h-3 w-3 ${userInterest?.is_interested ? 'fill-current' : ''}`} />
                          <span className="text-xs">
                            {userInterest?.is_interested ? 'Bookmarked' : 'Bookmark'}
                          </span>
                        </Button>
                        
                        {/* Collaboration Toggle Button */}
                        <Button
                          variant={userInterest?.wants_collaboration ? "default" : "outline"}
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleCollaborationToggle(
                              opportunity.id, 
                              userInterest?.is_interested || false, 
                              userInterest?.wants_collaboration || false
                            )
                          }}
                          disabled={isUpdating || isPassed}
                          className="flex items-center gap-1"
                        >
                          <HandHeart className={`h-3 w-3 ${userInterest?.wants_collaboration ? 'fill-current' : ''}`} />
                          <span className="text-xs">
                            {userInterest?.wants_collaboration ? 'Collaborating' : 'Collaborate'}
                          </span>
                        </Button>
                      </div>
                      

                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Funding Opportunity Dialog */}
      <Dialog open={!!editingOpportunity} onOpenChange={(open) => !open && setEditingOpportunity(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          {editingOpportunity && (
            <EditFundingOpportunityForm
              opportunity={editingOpportunity}
              onSuccess={handleEditSuccess}
              onCancel={() => setEditingOpportunity(null)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default FundingManagement
