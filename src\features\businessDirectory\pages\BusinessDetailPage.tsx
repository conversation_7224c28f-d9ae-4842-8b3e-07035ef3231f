import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { CarbonStatusBadge } from "@/components/ui/carbon-status-badge";
import { MapPin, Globe, Phone, Mail, Calendar, Building2, ArrowLeft, ExternalLink, Linkedin, X, ChevronLeft, ChevronRight, Package, BarChart3 } from "lucide-react";
import { supabase } from '@/integrations/supabase/client';
import BusinessLogoDisplay from '../components/BusinessLogoDisplay';
import type { Database } from '@/integrations/supabase/types';
import type { ProductImage } from '@/types/business-images.types';
import { NetZeroCategoryDisplay } from '@/components/netzero/NetZeroCategoryDisplay';
import { NetZeroCategoryService } from '@/services/netZeroCategoryService';
import type { NetZeroSubcategoryWithCategory } from '@/types/netzero-categories.types';
import { IndustryDisplay } from '@/components/industries/IndustryDisplay';
import { UKIndustryService } from '@/services/ukIndustryService';
import type { UKIndustryWithParent } from '@/types/uk-industries.types';
import { getNetZeroCategoryTheme, NetZeroCategoryIcon } from '@/utils/netZeroCategoryIcons';
import { getIndustryTheme, IndustryIcon } from '@/utils/industryIcons';
import { ProductService } from '@/services/productService';
import type { ProductWithCategories, CarbonMetric, BusinessProductsResponse } from '@/types/products.types';
import { CARBON_CALCULATION_STATUS_LABELS } from '@/types/products.types';

type Business = Database['public']['Tables']['businesses']['Row'];

// Location data structure
interface LocationInfo {
  id: string;
  name: string;
  slug: string;
  type: string;
  path: string;
}

export type BusinessItem = Business & {
  product_images_parsed?: ProductImage[];
  headquarters_location?: {
    id: string;
    name: string;
    slug: string;
    path: string;
  } | null;
  customer_locations?: LocationInfo[] | null;
  netZeroCategories?: NetZeroSubcategoryWithCategory[];
  primaryNetZeroCategory?: NetZeroSubcategoryWithCategory;
  mainIndustry?: UKIndustryWithParent | null;
  targetIndustries?: UKIndustryWithParent[];
  products?: ProductWithCategories[];
  businessCarbonMetrics?: CarbonMetric[];
  allCarbonMetrics?: CarbonMetric[];
};

// Custom X (Twitter) icon component
const XIcon = ({ className }: { className?: string }) => (
  <svg 
    viewBox="0 0 24 24" 
    className={className}
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
  </svg>
);

const BusinessDetailPage = () => {
  const { businessId } = useParams<{ businessId: string }>();
  const navigate = useNavigate();
  const [business, setBusiness] = useState<BusinessItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);

  // Handle keyboard navigation for image modal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (selectedImageIndex === null || !business?.product_images_parsed) return;
      
      if (e.key === 'Escape') {
        setSelectedImageIndex(null);
      } else if (e.key === 'ArrowLeft') {
        e.preventDefault();
        const newIndex = selectedImageIndex > 0 ? selectedImageIndex - 1 : business.product_images_parsed.length - 1;
        setSelectedImageIndex(newIndex);
      } else if (e.key === 'ArrowRight') {
        e.preventDefault();
        const newIndex = selectedImageIndex < business.product_images_parsed.length - 1 ? selectedImageIndex + 1 : 0;
        setSelectedImageIndex(newIndex);
      }
    };

    if (selectedImageIndex !== null) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [selectedImageIndex, business?.product_images_parsed]);

  const openImageModal = (index: number) => {
    setSelectedImageIndex(index);
  };

  const closeImageModal = () => {
    setSelectedImageIndex(null);
  };

  const goToPreviousImage = () => {
    if (!business?.product_images_parsed || selectedImageIndex === null) return;
    const newIndex = selectedImageIndex > 0 ? selectedImageIndex - 1 : business.product_images_parsed.length - 1;
    setSelectedImageIndex(newIndex);
  };

  const goToNextImage = () => {
    if (!business?.product_images_parsed || selectedImageIndex === null) return;
    const newIndex = selectedImageIndex < business.product_images_parsed.length - 1 ? selectedImageIndex + 1 : 0;
    setSelectedImageIndex(newIndex);
  };

  useEffect(() => {
    const fetchBusiness = async () => {
      if (!businessId) {
        setError('Business ID is required');
        setLoading(false);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('businesses')
          .select('*')
          .eq('id', businessId)
          .single();

        if (error) throw error;
        
        if (!data) {
          setError('Business not found');
          setLoading(false);
          return;
        }

        // Parse product images
        const typedData = data as Business;
        let businessWithParsedImages: BusinessItem = {
          ...typedData,
          product_images_parsed: typedData.product_images 
            ? (typeof typedData.product_images === 'string' 
                ? JSON.parse(typedData.product_images) 
                : typedData.product_images as unknown as ProductImage[])
            : []
        };

        // Fetch headquarters location details if available
        if (typedData.headquarters_location_id) {
          try {
            console.log('Fetching HQ location for business:', typedData.business_name, 'HQ ID:', typedData.headquarters_location_id);
            
            // Get headquarters location details - using any to bypass TypeScript issues
            const { data: hqLocationData, error: hqLocationError } = await supabase
              .from('locations' as any)
              .select('id, name, slug, type')
              .eq('id', typedData.headquarters_location_id)
              .single();

            if (!hqLocationError && hqLocationData) {
              console.log('Found HQ location data:', hqLocationData);
              
              // Cast the data to avoid TypeScript issues
              const locationData = hqLocationData as any;
              
              // Try to get full path using the get_location_path function
              try {
                const { data: pathData, error: pathError } = await (supabase as any)
                  .rpc('get_location_path', { location_id: typedData.headquarters_location_id });
                
                businessWithParsedImages.headquarters_location = {
                  id: locationData.id,
                  name: locationData.name,
                  slug: locationData.slug || '',
                  path: (!pathError && pathData) ? pathData : locationData.name
                };
              } catch (pathErr) {
                // Fallback to just name if path function fails
                businessWithParsedImages.headquarters_location = {
                  id: locationData.id,
                  name: locationData.name,
                  slug: locationData.slug || '',
                  path: locationData.name
                };
              }
            } else {
              console.warn('Could not fetch HQ location data:', hqLocationError);
            }
          } catch (err) {
            console.error('Error fetching HQ location:', err);
          }
        }

        // Try to fetch customer locations from business_customer_locations table
        try {
          console.log('Fetching customer locations for business:', typedData.business_name);
          
          const { data: customerLocationData, error: customerLocationError } = await supabase
            .from('business_customer_locations' as any)
            .select(`
              location_id,
              locations!inner (
                id,
                name,
                slug,
                type
              )
            `)
            .eq('business_id', typedData.id);

          if (!customerLocationError && customerLocationData && customerLocationData.length > 0) {
            console.log('Found customer location data:', customerLocationData);
            
            const customerLocations = await Promise.all(
              (customerLocationData as any[]).map(async (item: any) => {
                const location = item.locations;
                
                // Try to get full path for each customer location
                try {
                  const { data: pathData, error: pathError } = await (supabase as any)
                    .rpc('get_location_path', { location_id: location.id });
                  
                  return {
                    id: location.id,
                    name: location.name,
                    slug: location.slug || '',
                    type: location.type || '',
                    path: (!pathError && pathData) ? pathData : location.name
                  };
                } catch (pathErr) {
                  return {
                    id: location.id,
                    name: location.name,
                    slug: location.slug || '',
                    type: location.type || '',
                    path: location.name
                  };
                }
              })
            );
            
            businessWithParsedImages.customer_locations = customerLocations;
          } else {
            console.log('No customer locations found or error:', customerLocationError);
          }
        } catch (err) {
          console.error('Error fetching customer locations:', err);
        }

        // Fetch net-zero categories
        try {
          const { categories, primary_category } = await NetZeroCategoryService.getBusinessCategories(businessId);
          businessWithParsedImages.netZeroCategories = categories;
          businessWithParsedImages.primaryNetZeroCategory = primary_category;
        } catch (err) {
          console.error('Error fetching net-zero categories:', err);
          // Don't fail the whole page if categories can't be loaded
          businessWithParsedImages.netZeroCategories = [];
        }

        // Fetch business industries
        try {
          console.log('Fetching industries for business:', businessId);
          const { main_industry, target_industries } = await UKIndustryService.getBusinessIndustries(businessId);
          console.log('Fetched main_industry:', main_industry);
          console.log('Fetched target_industries:', target_industries);
          businessWithParsedImages.mainIndustry = main_industry;
          businessWithParsedImages.targetIndustries = target_industries;
        } catch (err) {
          console.error('Error fetching business industries:', err);
          // Don't fail the whole page if industries can't be loaded
          businessWithParsedImages.mainIndustry = null;
          businessWithParsedImages.targetIndustries = [];
        }

        // Fetch products
        try {
          const { products } = await ProductService.getBusinessProducts(businessId);
          businessWithParsedImages.products = products;
        } catch (err) {
          console.error('Error fetching products:', err);
          // Don't fail the whole page if products can't be loaded
          businessWithParsedImages.products = [];
        }

        // Fetch business-wide carbon metrics (not product-specific)
        try {
          const carbonMetrics = await ProductService.getCarbonMetrics(businessId);
          businessWithParsedImages.businessCarbonMetrics = carbonMetrics;
        } catch (err) {
          console.error('Error fetching business carbon metrics:', err);
          // Don't fail the whole page if carbon metrics can't be loaded
          businessWithParsedImages.businessCarbonMetrics = [];
        }

        // Fetch all carbon metrics
        try {
          const allCarbonMetrics = await ProductService.getAllCarbonMetrics(businessId);
          businessWithParsedImages.allCarbonMetrics = allCarbonMetrics;
        } catch (err) {
          console.error('Error fetching all carbon metrics:', err);
          // Don't fail the whole page if all carbon metrics can't be loaded
          businessWithParsedImages.allCarbonMetrics = [];
        }

        setBusiness(businessWithParsedImages);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred while fetching business details');
      } finally {
        setLoading(false);
      }
    };

    fetchBusiness();
  }, [businessId]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-8">
          <p>Loading business details...</p>
        </div>
      </div>
    );
  }

  if (error || !business) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Button 
            variant="ghost" 
            onClick={() => navigate('/business-directory')}
            className="gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Directory
          </Button>
        </div>
        <div className="p-8 border border-red-200 bg-red-50 text-red-700 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Business Not Found</h2>
          <p>{error || 'The requested business could not be found.'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header with back button */}
      <div className="mb-6">
        <Button 
          variant="ghost" 
          onClick={() => navigate('/business-directory')}
          className="gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Directory
        </Button>
      </div>

      {/* Business Header */}
      <div className="mb-8">
        <Card>
          <CardHeader>
            <div className="flex items-start gap-6">
              <BusinessLogoDisplay
                logoUrl={business.logo_url}
                businessName={business.business_name}
                size="xl"
                shape="square"
              />
              <div className="flex-1">
                <CardTitle className="text-3xl mb-2">{business.business_name}</CardTitle>
                <CardDescription className="text-lg mb-4">
                  {business.contact_email}
                </CardDescription>
                
                {/* Quick Contact Actions */}
                <div className="flex flex-wrap gap-3">
                  {business.website && (
                    <Button variant="outline" size="sm" asChild>
                      <a 
                        href={business.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="gap-2"
                      >
                        <Globe className="w-4 h-4" />
                        Visit Website
                        <ExternalLink className="w-3 h-3" />
                      </a>
                    </Button>
                  )}
                  
                  {business.contact_email && (
                    <Button variant="outline" size="sm" asChild>
                      <a href={`mailto:${business.contact_email}`} className="gap-2">
                        <Mail className="w-4 h-4" />
                        Email
                      </a>
                    </Button>
                  )}
                  
                  {business.contact_phone && (
                    <Button variant="outline" size="sm" asChild>
                      <a href={`tel:${business.contact_phone}`} className="gap-2">
                        <Phone className="w-4 h-4" />
                        Call
                      </a>
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>
      </div>

      <div className="grid gap-8 lg:grid-cols-3">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-8">
          {/* Products & Carbon Metrics */}
          {business.products && business.products.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="w-5 h-5" />
                  Products & Services ({business.products.length})
                </CardTitle>
                <CardDescription>
                  Products and services offered by this business, including their carbon impact status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 lg:grid-cols-2">
                  {business.products.map((product) => {
                    // Find carbon metrics for this specific product
                    const productMetrics = business.allCarbonMetrics?.filter(
                      metric => metric.product_id === product.id
                    ) || [];

                    // Find images linked to this specific product
                    const productImages = business.product_images_parsed?.filter(
                      image => image.product_id === product.id
                    ) || [];

                    return (
                      <div key={product.id} className="border rounded-lg p-4 space-y-4 h-fit">
                        {/* Product Header */}
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <h4 className="font-semibold text-lg">{product.name}</h4>
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-gray-600">Carbon Impact:</span>
                              <CarbonStatusBadge 
                                status={product.carbon_calculation_status}
                                size="md"
                                variant="modern"
                              />
                            </div>
                          </div>
                          {product.description && (
                            <p className="text-sm text-muted-foreground line-clamp-3">{product.description}</p>
                          )}
                        </div>

                        {/* Product Images */}
                        {productImages.length > 0 && (
                          <div>
                            <h5 className="text-sm font-medium text-gray-900 mb-2">Product Images</h5>
                            <div className="grid grid-cols-3 gap-2">
                              {productImages.slice(0, 6).map((image, index) => (
                                <div key={image.id} className="relative group">
                                  <div 
                                    className="relative aspect-square overflow-hidden rounded border border-gray-200 cursor-pointer hover:border-gray-300 hover:shadow-sm transition-all duration-200"
                                    onClick={() => {
                                      // Find the index in the full business images array for the modal
                                      const fullImageIndex = business.product_images_parsed?.findIndex(img => img.id === image.id);
                                      if (fullImageIndex !== undefined && fullImageIndex !== -1) {
                                        openImageModal(fullImageIndex);
                                      }
                                    }}
                                  >
                                    <img
                                      src={image.url}
                                      alt={image.description || image.filename}
                                      className="w-full h-full object-cover"
                                      title={image.description || "Click to view full size"}
                                    />
                                    {/* Hover overlay */}
                                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 flex items-center justify-center">
                                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/90 rounded-full p-1">
                                        <svg className="w-3 h-3 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                                        </svg>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                              {productImages.length > 6 && (
                                <div className="aspect-square rounded border border-gray-200 bg-gray-50 flex items-center justify-center text-xs text-muted-foreground">
                                  +{productImages.length - 6} more
                                </div>
                              )}
                            </div>
                          </div>
                        )}

                        {/* Net-Zero Categories */}
                        {product.categories && product.categories.length > 0 && (
                          <div>
                            <h5 className="text-sm font-medium text-gray-900 mb-2">Net-Zero Categories</h5>
                            <div className="flex flex-wrap gap-1.5">
                              {product.categories.slice(0, 4).map((category) => (
                                <Badge key={category.id} variant="outline" className="text-xs">
                                  {category.category.name}: {category.name}
                                </Badge>
                              ))}
                              {product.categories.length > 4 && (
                                <Badge variant="outline" className="text-xs">
                                  +{product.categories.length - 4} more
                                </Badge>
                              )}
                            </div>
                          </div>
                        )}

                        {/* Product-specific Carbon Metrics */}
                        {productMetrics.length > 0 && (
                          <div>
                            <h5 className="text-sm font-medium text-gray-900 mb-2 flex items-center gap-1">
                              <BarChart3 className="w-4 h-4" />
                              Carbon Impact Metrics
                            </h5>
                            <div className="space-y-2">
                              {productMetrics.slice(0, 3).map((metric) => (
                                <div key={metric.id} className="bg-gray-50 rounded-md p-3">
                                  <div className="flex items-start justify-between gap-2">
                                    <h6 className="font-medium text-sm text-gray-900">{metric.metric_name}</h6>
                                    <div className="text-right flex-shrink-0">
                                      <div className="font-mono text-sm font-bold text-green-700">
                                        {metric.co2_value} {metric.co2_unit} CO2e
                                      </div>
                                      {metric.per_unit_label && (
                                        <div className="text-xs text-muted-foreground">
                                          per {metric.per_unit_label}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                  
                                  {(metric.time_period || metric.confidence_level) && (
                                    <div className="flex gap-3 mt-1 text-xs text-muted-foreground">
                                      {metric.time_period && (
                                        <span>Period: {metric.time_period}</span>
                                      )}
                                      {metric.confidence_level && (
                                        <span>Confidence: {metric.confidence_level}/5</span>
                                      )}
                                    </div>
                                  )}
                                  
                                  {metric.baseline_description && (
                                    <p className="text-xs text-muted-foreground mt-2 line-clamp-2" title={metric.baseline_description}>
                                      {metric.baseline_description}
                                    </p>
                                  )}
                                </div>
                              ))}
                              {productMetrics.length > 3 && (
                                <div className="text-xs text-muted-foreground text-center py-1 bg-gray-50 rounded">
                                  +{productMetrics.length - 3} more carbon metrics
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Business-Wide Carbon Metrics */}
          {business.allCarbonMetrics && (() => {
            // Filter for business-wide metrics (no product_id)
            const businessWideMetrics = business.allCarbonMetrics.filter(metric => !metric.product_id);
            return businessWideMetrics.length > 0;
          })() && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Carbon Impact Metrics ({business.allCarbonMetrics.filter(metric => !metric.product_id).length})
                </CardTitle>
                <CardDescription>
                  Business-wide carbon impact measurements and achievements
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {business.allCarbonMetrics
                  .filter(metric => !metric.product_id)
                  .map((metric) => (
                    <div key={metric.id} className="border rounded-lg p-4 space-y-2">
                      <div className="flex items-start justify-between">
                        <h4 className="font-semibold">{metric.metric_name}</h4>
                        <div className="text-right">
                          <div className="font-mono text-lg font-bold text-green-700">
                            {metric.co2_value} {metric.co2_unit} CO2e
                            {metric.per_unit_label && (
                              <span className="text-sm text-muted-foreground ml-1">
                                per {metric.per_unit_label}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      {(metric.time_period || metric.confidence_level || metric.calculation_method) && (
                        <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                          {metric.time_period && (
                            <span>Period: {metric.time_period}</span>
                          )}
                          {metric.confidence_level && (
                            <span>Confidence: {metric.confidence_level}/5</span>
                          )}
                          {metric.calculation_method && (
                            <span>Method: {metric.calculation_method}</span>
                          )}
                        </div>
                      )}
                      
                      {metric.baseline_description && (
                        <p className="text-sm text-muted-foreground">
                          {metric.baseline_description}
                        </p>
                      )}
                    </div>
                  ))}
              </CardContent>
            </Card>
          )}

          {/* Net-Zero Categories */}
          {business.netZeroCategories && business.netZeroCategories.length > 0 && (
            <NetZeroCategoryDisplay
              categories={business.netZeroCategories}
              primaryCategory={business.primaryNetZeroCategory}
              title="Net-Zero Focus Areas"
              description="This business specializes in the following net-zero categories"
              showPrimary={true}
              variant="card"
            />
          )}

          {/* Business Industries */}
          {(business.mainIndustry || (business.targetIndustries && business.targetIndustries.length > 0)) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="w-5 h-5" />
                  Business Industries
                </CardTitle>
                <CardDescription>
                  Main industry and relevant markets
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {business.mainIndustry && (
                  <div className="space-y-2">
                    <h4 className="font-semibold text-sm">Main Industry:</h4>
                    <div className="flex items-center gap-2">
                      {(() => {
                        const parentIndustry = business.mainIndustry?.parent;
                        const theme = parentIndustry ? getIndustryTheme(parentIndustry.name) : null;
                        return (
                          <Badge variant="default" className={`flex items-center gap-1 border ${theme ? `${theme.borderColor} ${theme.textColor}` : 'bg-blue-600 hover:bg-blue-700 text-white border-blue-600'}`}>
                            {theme && <IndustryIcon industryName={parentIndustry!.name} size="sm" className="w-3 h-3" />}
                            {business.mainIndustry!.name}
                          </Badge>
                        );
                      })()}
                    </div>
                  </div>
                )}

                {business.targetIndustries && business.targetIndustries.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-semibold text-sm">Relevant Industries ({business.targetIndustries.length}):</h4>
                    <div className="flex flex-wrap gap-2">
                      {business.targetIndustries.map((industry) => {
                        const parentIndustry = industry.parent;
                        const theme = parentIndustry ? getIndustryTheme(parentIndustry.name) : null;
                        return (
                          <Badge 
                            key={industry.id} 
                            variant="outline" 
                            className={`flex items-center gap-1 border ${theme ? `${theme.borderColor} ${theme.textColor}` : 'border-green-300 text-green-700'}`}
                          >
                            {theme && <IndustryIcon industryName={parentIndustry!.name} size="sm" className="w-3 h-3" />}
                            {industry.name}
                          </Badge>
                        );
                      })}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Contact Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {business.contact_email && (
                <div className="flex items-start gap-3">
                  <Mail className="w-4 h-4 text-muted-foreground mt-1" />
                  <div>
                    <p className="text-sm font-medium">Email</p>
                    <a 
                      href={`mailto:${business.contact_email}`}
                      className="text-sm text-primary hover:underline"
                    >
                      {business.contact_email}
                    </a>
                  </div>
                </div>
              )}

              {business.contact_phone && (
                <div className="flex items-start gap-3">
                  <Phone className="w-4 h-4 text-muted-foreground mt-1" />
                  <div>
                    <p className="text-sm font-medium">Phone</p>
                    <a 
                      href={`tel:${business.contact_phone}`}
                      className="text-sm text-primary hover:underline"
                    >
                      {business.contact_phone}
                    </a>
                  </div>
                </div>
              )}

              {business.website && (
                <div className="flex items-start gap-3">
                  <Globe className="w-4 h-4 text-muted-foreground mt-1" />
                  <div>
                    <p className="text-sm font-medium">Website</p>
                    <a 
                      href={business.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-primary hover:underline flex items-center gap-1"
                    >
                      {business.website.replace(/^https?:\/\//, '')}
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  </div>
                </div>
              )}

              <Separator />

              {/* Headquarters Location */}
              {business.headquarters_location && (
                <div className="flex items-start gap-3">
                  <Building2 className="w-4 h-4 text-muted-foreground mt-1" />
                  <div>
                    <p className="text-sm font-medium">Headquarters Location</p>
                    <div className="text-sm">
                      <p className="font-medium text-green-600">{business.headquarters_location.name}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Show headquarters_location_id as debug info if no structured location data */}
              {business.headquarters_location_id && !business.headquarters_location && (
                <div className="flex items-start gap-3">
                  <Building2 className="w-4 h-4 text-muted-foreground mt-1" />
                  <div>
                    <p className="text-sm font-medium">Headquarters Location ID</p>
                    <div className="text-xs text-muted-foreground font-mono">
                      {business.headquarters_location_id}
                    </div>
                  </div>
                </div>
              )}

              {/* Basic Address Information */}
              {(business.address_line_1 || business.city || business.postcode) && (
                <div className="flex items-start gap-3">
                  <MapPin className="w-4 h-4 text-muted-foreground mt-1" />
                  <div>
                    <p className="text-sm font-medium">Address</p>
                    <div className="text-sm text-muted-foreground">
                      {business.address_line_1 && <p>{business.address_line_1}</p>}
                      {business.address_line_2 && <p>{business.address_line_2}</p>}
                      <p>
                        {[business.city, business.postcode].filter(Boolean).join(', ')}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Customer Service Areas */}
              {business.customer_locations && business.customer_locations.length > 0 && (
                <div className="flex items-start gap-3">
                  <MapPin className="w-4 h-4 text-muted-foreground mt-1" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">Service Areas ({business.customer_locations.length} locations)</p>
                    <div className="text-sm mt-1">
                      <div className="grid gap-1">
                        {business.customer_locations.slice(0, 5).map((location) => (
                          <div key={location.id} className="flex items-center gap-2">
                            <span className="w-1 h-1 bg-current rounded-full opacity-60"></span>
                            <span className="font-medium text-green-600">{location.name}</span>
                          </div>
                        ))}
                        {business.customer_locations.length > 5 && (
                          <div className="text-xs opacity-60 mt-1">
                            ... and {business.customer_locations.length - 5} more locations
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Social Media */}
          {(business.twitter || business.linkedin) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Social Media</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {business.twitter && (
                  <a 
                    href={business.twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors group"
                  >
                    <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100 text-gray-800 group-hover:bg-gray-200 transition-colors">
                      <XIcon className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-sm">X</p>
                      <p className="text-xs text-muted-foreground">Follow us on X</p>
                    </div>
                    <ExternalLink className="w-4 h-4 text-muted-foreground" />
                  </a>
                )}
                
                {business.linkedin && (
                  <a 
                    href={business.linkedin}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors group"
                  >
                    <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100 text-gray-900 group-hover:bg-gray-200 transition-colors">
                      <Linkedin className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-sm">LinkedIn</p>
                      <p className="text-xs text-muted-foreground">Connect with us on LinkedIn</p>
                    </div>
                    <ExternalLink className="w-4 h-4 text-muted-foreground" />
                  </a>
                )}
              </CardContent>
            </Card>
          )}

          {/* Business Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Business Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Calendar className="w-4 h-4" />
                Added {new Date(business.created_at || '').toLocaleDateString()}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Image Modal/Lightbox */}
      {selectedImageIndex !== null && business?.product_images_parsed && (
        <div className="fixed inset-0 z-50 bg-black/95 flex items-center justify-center p-2 sm:p-4 lg:p-8">
          {/* Close button */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 sm:top-4 sm:right-4 text-white hover:bg-white/20 z-10 h-10 w-10"
            onClick={closeImageModal}
          >
            <X className="w-6 h-6" />
          </Button>

          {/* Navigation buttons */}
          {business.product_images_parsed.length > 1 && (
            <>
              <Button
                variant="ghost"
                size="icon"
                className="absolute left-2 sm:left-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20 z-10 h-12 w-12"
                onClick={goToPreviousImage}
              >
                <ChevronLeft className="w-8 h-8" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-2 sm:right-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20 z-10 h-12 w-12"
                onClick={goToNextImage}
              >
                <ChevronRight className="w-8 h-8" />
              </Button>
            </>
          )}

          {/* Main image */}
          <div className="relative max-w-[75vw] max-h-[75vh] w-full h-full flex items-center justify-center">
            <img
              src={business.product_images_parsed[selectedImageIndex].url}
              alt={business.product_images_parsed[selectedImageIndex].description || business.product_images_parsed[selectedImageIndex].filename}
              className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
              style={{ maxWidth: '75vw', maxHeight: '75vh' }}
              onClick={closeImageModal}
            />
            
            {/* Image info */}
            {business.product_images_parsed[selectedImageIndex].description && (
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/60 to-transparent text-white p-6 rounded-b-lg">
                <p className="text-center text-lg">{business.product_images_parsed[selectedImageIndex].description}</p>
              </div>
            )}

            {/* Image counter */}
            {business.product_images_parsed.length > 1 && (
              <div className="absolute top-4 left-4 bg-black/80 text-white px-4 py-2 rounded-full text-sm font-medium backdrop-blur-sm">
                {selectedImageIndex + 1} / {business.product_images_parsed.length}
              </div>
            )}
          </div>

          {/* Click outside to close */}
          <div 
            className="absolute inset-0 -z-10" 
            onClick={closeImageModal}
          />
        </div>
      )}
    </div>
  );
};

export default BusinessDetailPage;
