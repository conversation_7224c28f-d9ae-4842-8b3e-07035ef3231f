import React from 'react';
import { 
  Factory, 
  Briefcase, 
  Zap, 
  TreePine, 
  Building, 
  Heart, 
  Users,
  LucideIcon
} from 'lucide-react';

export interface IndustryTheme {
  icon: LucideIcon;
  bgColor: string;
  textColor: string;
  borderColor: string;
  hoverBgColor: string;
  name: string;
}

// Industry theme mapping based on parent industry names
export const INDUSTRY_THEMES: Record<string, IndustryTheme> = {
  'Manufacturing & Production': {
    icon: Factory,
    bgColor: '',
    textColor: 'text-blue-700',
    borderColor: 'border-blue-200',
    hoverBgColor: 'hover:bg-gray-50',
    name: 'Manufacturing & Production'
  },
  'Services': {
    icon: Briefcase,
    bgColor: '',
    textColor: 'text-purple-700',
    borderColor: 'border-purple-200',
    hoverBgColor: 'hover:bg-gray-50',
    name: 'Services'
  },
  'Energy & Utilities': {
    icon: Zap,
    bgColor: '',
    textColor: 'text-yellow-700',
    borderColor: 'border-yellow-200',
    hoverBgColor: 'hover:bg-gray-50',
    name: 'Energy & Utilities'
  },
  'Primary Industries': {
    icon: TreePine,
    bgColor: '',
    textColor: 'text-green-700',
    borderColor: 'border-green-200',
    hoverBgColor: 'hover:bg-gray-50',
    name: 'Primary Industries'
  },
  'Construction & Real Estate': {
    icon: Building,
    bgColor: '',
    textColor: 'text-orange-700',
    borderColor: 'border-orange-200',
    hoverBgColor: 'hover:bg-gray-50',
    name: 'Construction & Real Estate'
  },
  'Healthcare & Education': {
    icon: Heart,
    bgColor: '',
    textColor: 'text-red-700',
    borderColor: 'border-red-200',
    hoverBgColor: 'hover:bg-gray-50',
    name: 'Healthcare & Education'
  },
  'Social Care': {
    icon: Users,
    bgColor: '',
    textColor: 'text-pink-700',
    borderColor: 'border-pink-200',
    hoverBgColor: 'hover:bg-gray-50',
    name: 'Social Care'
  }
};

// Helper function to get theme for an industry
export const getIndustryTheme = (industryName: string): IndustryTheme => {
  return INDUSTRY_THEMES[industryName] || {
    icon: Briefcase,
    bgColor: '',
    textColor: 'text-gray-700',
    borderColor: 'border-gray-200',
    hoverBgColor: 'hover:bg-gray-50',
    name: industryName
  };
};

// Helper function to get theme by industry ID (requires industry data lookup)
export const getIndustryThemeById = (industries: any[], industryId: string): IndustryTheme => {
  // Find the industry and its parent
  for (const parent of industries) {
    if (parent.id === industryId) {
      return getIndustryTheme(parent.name);
    }
    for (const child of parent.children || []) {
      if (child.id === industryId) {
        return getIndustryTheme(parent.name);
      }
    }
  }
  
  // Fallback theme
  return {
    icon: Briefcase,
    bgColor: '',
    textColor: 'text-gray-700',
    borderColor: 'border-gray-200',
    hoverBgColor: 'hover:bg-gray-50',
    name: 'Unknown'
  };
};

// Component to render an industry icon with theme
export const IndustryIcon: React.FC<{
  industryName: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}> = ({ industryName, size = 'lg', className = '' }) => {
  const theme = getIndustryTheme(industryName);
  const Icon = theme.icon;
  
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-8 h-8'
  };
  
  return (
    <Icon className={`${sizeClasses[size]} ${theme.textColor} ${className}`} />
  );
}; 